version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    container_name: odoo_db
    environment:
      POSTGRES_DB: odoo
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - odoo_db_data:/var/lib/postgresql/data/pgdata
    ports:
      - "5433:5432"
    restart: unless-stopped
    networks:
      - odoo_network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: odoo_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "8081:80"
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - odoo_network

  # Odoo Application (optional - for containerized development)
  odoo:
    image: odoo:18.0
    container_name: odoo_app
    environment:
      HOST: db
      USER: odoo
      PASSWORD: odoo
    volumes:
      - ./addons:/mnt/extra-addons/addons:ro
      - ./custom:/mnt/extra-addons/custom:ro
      - ./ent:/mnt/extra-addons/ent:ro
      - ./config:/etc/odoo:ro
      - odoo_data:/var/lib/odoo
    ports:
      - "8069:8069"
    depends_on:
      - db
    restart: unless-stopped
    networks:
      - odoo_network
    command: >
      bash -c "
        echo 'Waiting for PostgreSQL to be ready...' &&
        while ! nc -z db 5432; do sleep 1; done &&
        echo 'PostgreSQL is ready!' &&
        odoo --addons-path=/mnt/extra-addons/addons,/mnt/extra-addons/custom,/mnt/extra-addons/ent,/usr/lib/python3/dist-packages/odoo/addons
      "

  # Redis for caching (optional but recommended for production-like development)
  redis:
    image: redis:7-alpine
    container_name: odoo_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - odoo_network

  # Mailhog for email testing (optional)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: odoo_mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    restart: unless-stopped
    networks:
      - odoo_network

volumes:
  odoo_db_data:
    driver: local
  pgadmin_data:
    driver: local
  odoo_data:
    driver: local
  redis_data:
    driver: local

networks:
  odoo_network:
    driver: bridge
